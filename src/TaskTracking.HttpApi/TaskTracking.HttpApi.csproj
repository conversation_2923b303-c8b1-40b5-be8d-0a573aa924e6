<Project Sdk="Microsoft.NET.Sdk">

  <Import Project="..\..\common.props" />

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <Nullable>enable</Nullable>
    <RootNamespace>TaskTracking</RootNamespace>
  </PropertyGroup>

  <ItemGroup>
    <ProjectReference Include="..\TaskTracking.Application.Contracts\TaskTracking.Application.Contracts.csproj" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Volo.Abp.Account.HttpApi" Version="9.1.3" />
    <PackageReference Include="Volo.Abp.Identity.HttpApi" Version="9.1.3" />
    <PackageReference Include="Volo.Abp.PermissionManagement.HttpApi" Version="9.1.3" />
    <PackageReference Include="Volo.Abp.TenantManagement.HttpApi" Version="9.1.3" />
    <PackageReference Include="Volo.Abp.FeatureManagement.HttpApi" Version="9.1.3" />
    <PackageReference Include="Volo.Abp.SettingManagement.HttpApi" Version="9.1.3" />
  </ItemGroup>

</Project>
