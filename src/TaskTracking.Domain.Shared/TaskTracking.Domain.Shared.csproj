<Project Sdk="Microsoft.NET.Sdk">

  <Import Project="..\..\common.props" />

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <Nullable>enable</Nullable>
    <RootNamespace>TaskTracking</RootNamespace>
    <GenerateEmbeddedFilesManifest>true</GenerateEmbeddedFilesManifest>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Volo.Abp.Identity.Domain.Shared" Version="9.1.3" />
    <PackageReference Include="Volo.Abp.BackgroundJobs.Domain.Shared" Version="9.1.3" />
    <PackageReference Include="Volo.Abp.AuditLogging.Domain.Shared" Version="9.1.3" />
    <PackageReference Include="Volo.Abp.TenantManagement.Domain.Shared" Version="9.1.3" />
    <PackageReference Include="Volo.Abp.FeatureManagement.Domain.Shared" Version="9.1.3" />
    <PackageReference Include="Volo.Abp.PermissionManagement.Domain.Shared" Version="9.1.3" />
    <PackageReference Include="Volo.Abp.SettingManagement.Domain.Shared" Version="9.1.3" />
    <PackageReference Include="Volo.Abp.OpenIddict.Domain.Shared" Version="9.1.3" />
  </ItemGroup>

  <ItemGroup>
    <EmbeddedResource Include="Localization\TaskTracking\*.json" />
    <Content Remove="Localization\TaskTracking\*.json" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.Extensions.FileProviders.Embedded" Version="9.0.0" />
  </ItemGroup>

</Project>
