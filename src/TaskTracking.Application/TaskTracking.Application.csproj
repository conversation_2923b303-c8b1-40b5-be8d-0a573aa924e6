<Project Sdk="Microsoft.NET.Sdk">

  <Import Project="..\..\common.props" />

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <Nullable>enable</Nullable>
    <RootNamespace>TaskTracking</RootNamespace>
  </PropertyGroup>

  <ItemGroup>
    <ProjectReference Include="..\TaskTracking.Domain\TaskTracking.Domain.csproj" />
    <ProjectReference Include="..\TaskTracking.Application.Contracts\TaskTracking.Application.Contracts.csproj" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Volo.Abp.Account.Application" Version="9.1.3" />
    <PackageReference Include="Volo.Abp.Identity.Application" Version="9.1.3" />
    <PackageReference Include="Volo.Abp.PermissionManagement.Application" Version="9.1.3" />
    <PackageReference Include="Volo.Abp.TenantManagement.Application" Version="9.1.3" />
    <PackageReference Include="Volo.Abp.FeatureManagement.Application" Version="9.1.3" />
    <PackageReference Include="Volo.Abp.SettingManagement.Application" Version="9.1.3" />
  </ItemGroup>

</Project>
