<Project Sdk="Microsoft.NET.Sdk.Web">

  <Import Project="..\..\common.props" />

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <Nullable>enable</Nullable>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Serilog.AspNetCore" Version="8.0.0" />
    <PackageReference Include="Serilog.Sinks.Async" Version="1.5.0" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.AspNetCore.Components.WebAssembly.Server" Version="9.0.0" />
    <PackageReference Include="Volo.Abp.Autofac" Version="9.1.3" />
    <PackageReference Include="Volo.Abp.AspNetCore.Mvc.UI.Bundling" Version="9.1.3" />
    <PackageReference Include="Volo.Abp.AspNetCore.Components.WebAssembly.LeptonXLiteTheme.Bundling" Version="4.1.0-preview*" />
    <ProjectReference Include="..\TaskTracking.Blazor.Client\TaskTracking.Blazor.Client.csproj" />
  </ItemGroup>

</Project>
