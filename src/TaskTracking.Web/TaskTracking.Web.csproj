<Project Sdk="Microsoft.NET.Sdk.Web">

  <Import Project="..\..\common.props" />

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <Nullable>enable</Nullable>
    <RootNamespace>TaskTracking.Web</RootNamespace>
    <AssetTargetFallback>$(AssetTargetFallback);portable-net45+win8+wp8+wpa81;</AssetTargetFallback>
    <AutoGenerateBindingRedirects>true</AutoGenerateBindingRedirects>
    <GenerateBindingRedirectsOutputType>true</GenerateBindingRedirectsOutputType>
    <GenerateRuntimeConfigurationFiles>true</GenerateRuntimeConfigurationFiles>
    <MvcRazorExcludeRefAssembliesFromPublish>false</MvcRazorExcludeRefAssembliesFromPublish>
    <PreserveCompilationReferences>true</PreserveCompilationReferences>
    <UserSecretsId>TaskTracking-4681b4fd-151f-4221-84a4-929d86723e4c</UserSecretsId>
  </PropertyGroup>

  <ItemGroup>
    <Compile Remove="Logs\**" />
    <Content Remove="Logs\**" />
    <EmbeddedResource Remove="Logs\**" />
    <None Remove="Logs\**" />
  </ItemGroup>

  <ItemGroup>
    <None Update="Pages\**\*.js">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="Pages\**\*.css">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
  </ItemGroup>

  <ItemGroup Condition="Exists('./openiddict.pfx')">
    <None Remove="openiddict.pfx" />
    <EmbeddedResource Include="openiddict.pfx">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </EmbeddedResource>
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Serilog.AspNetCore" Version="8.0.2" />
    <PackageReference Include="Serilog.Sinks.Async" Version="2.0.0" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Volo.Abp.AspNetCore.Mvc.UI.Theme.LeptonXLite" Version="4.1.0-preview*" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\TaskTracking.Application\TaskTracking.Application.csproj" />
    <ProjectReference Include="..\TaskTracking.HttpApi\TaskTracking.HttpApi.csproj" />
    <ProjectReference Include="..\TaskTracking.EntityFrameworkCore\TaskTracking.EntityFrameworkCore.csproj" />
    <PackageReference Include="Volo.Abp.Autofac" Version="9.1.3" />
    <PackageReference Include="Volo.Abp.Swashbuckle" Version="9.1.3" />
    <PackageReference Include="Volo.Abp.AspNetCore.Serilog" Version="9.1.3" />
    <PackageReference Include="Volo.Abp.Identity.Web" Version="9.1.3" />
    <PackageReference Include="Volo.Abp.Account.Web.OpenIddict" Version="9.1.3" />
    <PackageReference Include="Volo.Abp.TenantManagement.Web" Version="9.1.3" />
    <PackageReference Include="Volo.Abp.SettingManagement.Web" Version="9.1.3" />
  </ItemGroup>

</Project>
