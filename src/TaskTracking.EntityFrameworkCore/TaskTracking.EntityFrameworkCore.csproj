<Project Sdk="Microsoft.NET.Sdk">

  <Import Project="..\..\common.props" />

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <Nullable>enable</Nullable>
    <RootNamespace>TaskTracking</RootNamespace>
  </PropertyGroup>

  <ItemGroup>
    <ProjectReference Include="..\TaskTracking.Domain\TaskTracking.Domain.csproj" />
    <PackageReference Include="Volo.Abp.EntityFrameworkCore.MySQL" Version="9.1.3" />
    <PackageReference Include="Volo.Abp.PermissionManagement.EntityFrameworkCore" Version="9.1.3" />
    <PackageReference Include="Volo.Abp.SettingManagement.EntityFrameworkCore" Version="9.1.3" />
    <PackageReference Include="Volo.Abp.Identity.EntityFrameworkCore" Version="9.1.3" />
    <PackageReference Include="Volo.Abp.BackgroundJobs.EntityFrameworkCore" Version="9.1.3" />
    <PackageReference Include="Volo.Abp.AuditLogging.EntityFrameworkCore" Version="9.1.3" />
    <PackageReference Include="Volo.Abp.TenantManagement.EntityFrameworkCore" Version="9.1.3" />
    <PackageReference Include="Volo.Abp.FeatureManagement.EntityFrameworkCore" Version="9.1.3" />
    <PackageReference Include="Volo.Abp.OpenIddict.EntityFrameworkCore" Version="9.1.3" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.EntityFrameworkCore.Tools" Version="9.0.0">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers</IncludeAssets>
    </PackageReference>
  </ItemGroup>

</Project>
