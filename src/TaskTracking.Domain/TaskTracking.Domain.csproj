<Project Sdk="Microsoft.NET.Sdk">

  <Import Project="..\..\common.props" />

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <Nullable>enable</Nullable>
    <RootNamespace>TaskTracking</RootNamespace>
  </PropertyGroup>

  <ItemGroup>
    <ProjectReference Include="..\TaskTracking.Domain.Shared\TaskTracking.Domain.Shared.csproj" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Volo.Abp.Emailing" Version="9.1.3" />
    <PackageReference Include="Volo.Abp.Identity.Domain" Version="9.1.3" />
    <PackageReference Include="Volo.Abp.PermissionManagement.Domain.Identity" Version="9.1.3" />
    <PackageReference Include="Volo.Abp.BackgroundJobs.Domain" Version="9.1.3" />
    <PackageReference Include="Volo.Abp.AuditLogging.Domain" Version="9.1.3" />
    <PackageReference Include="Volo.Abp.TenantManagement.Domain" Version="9.1.3" />
    <PackageReference Include="Volo.Abp.FeatureManagement.Domain" Version="9.1.3" />
    <PackageReference Include="Volo.Abp.SettingManagement.Domain" Version="9.1.3" />
    <PackageReference Include="Volo.Abp.OpenIddict.Domain" Version="9.1.3" />
    <PackageReference Include="Volo.Abp.PermissionManagement.Domain.OpenIddict" Version="9.1.3" />
  </ItemGroup>

</Project>
