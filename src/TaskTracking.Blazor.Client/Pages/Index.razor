@page "/"
@inherits TaskTrackingComponentBase
@inject AuthenticationStateProvider AuthenticationStateProvider

<div class="islamic-pattern-bg">
    <!-- Header Section -->
    <div class="hero-section mb-4">
        <MudContainer MaxWidth="MaxWidth.Large">
            <div class="text-center py-5">
                <MudIcon Icon="@Icons.Material.Filled.Dashboard"
                         Class="hero-icon mb-3"
                         Size="Size.Large" />
                <MudText Typo="Typo.h3" Class="hero-title mb-2">
                    @L["WelcomeToDashboard"]
                </MudText>
                <MudText Typo="Typo.h6" Class="hero-subtitle">
                    @L["ManageTaskGroupsEfficiently"]
                </MudText>
            </div>
        </MudContainer>
    </div>

    <!-- Task Groups Section -->
    <MudContainer>
        <div class="d-flex justify-content-between align-items-center mb-4">
            <MudText Typo="Typo.h4" Class="section-title">
                <MudIcon Icon="@Icons.Material.Filled.Group" Class="me-2" />
                @L["ActiveTaskGroups"]
            </MudText>
            <MudButton Variant="Variant.Filled"
                       Color="Color.Primary"
                       StartIcon="@Icons.Material.Filled.Add"
                       Class="create-btn"
                       OnClick="@(() => NavigationManager.NavigateTo("/task-groups/create"))">
                @L["CreateNewGroup"]
            </MudButton>
        </div>

        @if (IsLoading)
        {
            <div class="text-center py-5">
                <MudProgressCircular Color="Color.Primary" Size="Size.Large" Indeterminate="true" />
                <MudText Typo="Typo.body1" Class="mt-3">@L["LoadingTaskGroups"]</MudText>
            </div>
        }
        else if (TaskGroups?.Items?.Any() == true)
        {
            <MudGrid Class="pb-4">
                @foreach (var taskGroup in TaskGroups.Items)
                {
                    <MudItem xs="12" sm="6" md="4">
                        <TaskGroupCard TaskGroup="taskGroup" />
                    </MudItem>
                }
            </MudGrid>

            @if (TaskGroups.TotalCount > TaskGroups.Items.Count)
            {
                <div class="text-center mt-4">
                    <MudButton Variant="Variant.Outlined"
                               Color="Color.Primary"
                               OnClick="LoadMoreTaskGroups"
                               Disabled="IsLoadingMore">
                        @if (IsLoadingMore)
                        {
                            <MudProgressCircular Size="Size.Small" Indeterminate="true" Class="me-2" />
                        }
                        @L["LoadMore"]
                    </MudButton>
                </div>
            }
        }
        else
        {
            <div class="empty-state text-center py-5">
                <MudIcon Icon="@Icons.Material.Filled.FolderOpen"
                         Size="Size.Large"
                         Class="empty-icon mb-3" />
                <MudText Typo="Typo.h5" Class="mb-2">@L["NoTaskGroups"]</MudText>
                <MudText Typo="Typo.body1" Class="text-muted mb-4">
                    @L["StartByCreatingTaskGroup"]
                </MudText>
                <MudButton Variant="Variant.Filled"
                           Color="Color.Primary"
                           StartIcon="@Icons.Material.Filled.Add"
                   OnClick="@(() => NavigationManager.NavigateTo("/task-groups/create"))">
                    @L["CreateFirstTaskGroup"]
                </MudButton>
            </div>
        }
    </MudContainer>
</div>