<Project Sdk="Microsoft.NET.Sdk">

  <Import Project="..\..\common.props" />

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <Nullable>enable</Nullable>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.NET.Test.Sdk" Version="17.11.1.0" />
    <PackageReference Include="HtmlAgilityPack" Version="1.11.67.0" />
    <ProjectReference Include="..\TaskTracking.Application.Tests\TaskTracking.Application.Tests.csproj" />
    <ProjectReference Include="..\..\src\TaskTracking.Web\TaskTracking.Web.csproj" />
    <PackageReference Include="Volo.Abp.AspNetCore.TestBase" Version="9.1.3" />
    <ProjectReference Include="..\TaskTracking.EntityFrameworkCore.Tests\TaskTracking.EntityFrameworkCore.Tests.csproj" />
  </ItemGroup>

  <ItemGroup>
    <Content Include="xunit.runner.json">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
    </Content>
  </ItemGroup>

  <!-- https://github.com/NuGet/Home/issues/4412 -->
  <Target Name="CopyDepsFiles" AfterTargets="Build" Condition="'$(TargetFramework)'!=''">
    <ItemGroup>
      <DepsFilePaths Include="$([System.IO.Path]::ChangeExtension('%(_ResolvedProjectReferencePaths.FullPath)', '.deps.json'))" />
    </ItemGroup>

    <Copy SourceFiles="%(DepsFilePaths.FullPath)" DestinationFolder="$(OutputPath)" Condition="Exists('%(DepsFilePaths.FullPath)')" />
  </Target>

</Project>
