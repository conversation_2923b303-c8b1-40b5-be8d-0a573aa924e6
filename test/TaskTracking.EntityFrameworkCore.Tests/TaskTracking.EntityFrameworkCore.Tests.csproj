<Project Sdk="Microsoft.NET.Sdk">

  <Import Project="..\..\common.props" />

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <Nullable>enable</Nullable>
    <RootNamespace>TaskTracking</RootNamespace>
  </PropertyGroup>

  <ItemGroup>
    <ProjectReference Include="..\..\src\TaskTracking.EntityFrameworkCore\TaskTracking.EntityFrameworkCore.csproj" />
    <ProjectReference Include="..\TaskTracking.Application.Tests\TaskTracking.Application.Tests.csproj" />
    <PackageReference Include="Volo.Abp.EntityFrameworkCore.Sqlite" Version="9.1.3" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.NET.Test.Sdk" Version="17.11.1.0" />
  </ItemGroup>

</Project>
