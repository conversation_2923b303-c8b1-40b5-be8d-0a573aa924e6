
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 16
VisualStudioVersion = 16.0.29020.237
MinimumVisualStudioVersion = 10.0.40219.1
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "TaskTracking.Domain", "src\TaskTracking.Domain\TaskTracking.Domain.csproj", "{554AD327-6DBA-4F8F-96F8-81CE7A0C863F}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "TaskTracking.Application", "src\TaskTracking.Application\TaskTracking.Application.csproj", "{1A94A50E-06DC-43C1-80B5-B662820EC3EB}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "TaskTracking.EntityFrameworkCore", "src\TaskTracking.EntityFrameworkCore\TaskTracking.EntityFrameworkCore.csproj", "{C956DD76-69C8-4A9C-83EA-D17DF83340FD}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "TaskTracking.Web", "src\TaskTracking.Web\TaskTracking.Web.csproj", "{068855E8-9240-4F1A-910B-CF825794513B}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "src", "src", "{CA9AC87F-097E-4F15-8393-4BC07735A5B0}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "test", "test", "{04DBDB01-70F4-4E06-B468-8F87850B22BE}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "TaskTracking.Application.Tests", "test\TaskTracking.Application.Tests\TaskTracking.Application.Tests.csproj", "{50B2631D-129C-47B3-A587-029CCD6099BC}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "TaskTracking.Web.Tests", "test\TaskTracking.Web.Tests\TaskTracking.Web.Tests.csproj", "{5F1B28C6-8D0C-4155-92D0-252F7EA5F674}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "TaskTracking.Domain.Shared", "src\TaskTracking.Domain.Shared\TaskTracking.Domain.Shared.csproj", "{42F719ED-8413-4895-B5B4-5AB56079BC66}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "TaskTracking.Application.Contracts", "src\TaskTracking.Application.Contracts\TaskTracking.Application.Contracts.csproj", "{520659C8-C734-4298-A3DA-B539DB9DFC0B}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "TaskTracking.HttpApi", "src\TaskTracking.HttpApi\TaskTracking.HttpApi.csproj", "{4164BDF7-F527-4E85-9CE6-E3C2D7426A27}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "TaskTracking.HttpApi.Client", "src\TaskTracking.HttpApi.Client\TaskTracking.HttpApi.Client.csproj", "{3B5A0094-670D-4BB1-BFDD-61B88A8773DC}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "TaskTracking.EntityFrameworkCore.Tests", "test\TaskTracking.EntityFrameworkCore.Tests\TaskTracking.EntityFrameworkCore.Tests.csproj", "{1FE30EB9-74A9-47F5-A9F6-7B1FAB672D81}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "TaskTracking.TestBase", "test\TaskTracking.TestBase\TaskTracking.TestBase.csproj", "{91853F21-9CD9-4132-BC29-A7D5D84FFFE7}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "TaskTracking.Domain.Tests", "test\TaskTracking.Domain.Tests\TaskTracking.Domain.Tests.csproj", "{E512F4D9-9375-480F-A2F6-A46509F9D824}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "TaskTracking.HttpApi.Client.ConsoleTestApp", "test\TaskTracking.HttpApi.Client.ConsoleTestApp\TaskTracking.HttpApi.Client.ConsoleTestApp.csproj", "{EF480016-9127-4916-8735-D2466BDBC582}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "TaskTracking.DbMigrator", "src\TaskTracking.DbMigrator\TaskTracking.DbMigrator.csproj", "{AA94D832-1CCC-4715-95A9-A483F23A1A5D}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "TaskTracking.Common", "TaskTracking.Common", "{C7F8F66B-36F6-4087-9425-AED1F8D964ED}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "TaskTracking.Application", "TaskTracking.Application", "{AD9644A0-B8AA-40EC-A8A2-D28BE8B80B9E}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "TaskTracking.HttpApi.Host", "src\TaskTracking.HttpApi.Host\TaskTracking.HttpApi.Host.csproj", "{9E701B08-5AE8-4C25-B1B4-0E205A5235EE}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "TaskTracking.Blazor", "src\TaskTracking.Blazor\TaskTracking.Blazor.csproj", "{1D2A6256-9BAC-475C-91A5-45C0AE3CEFB5}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "TaskTracking.Blazor.Client", "src\TaskTracking.Blazor.Client\TaskTracking.Blazor.Client.csproj", "{D24A8C98-1F46-4758-9651-F5990D3D4673}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Release|Any CPU = Release|Any CPU
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{554AD327-6DBA-4F8F-96F8-81CE7A0C863F}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{554AD327-6DBA-4F8F-96F8-81CE7A0C863F}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{554AD327-6DBA-4F8F-96F8-81CE7A0C863F}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{554AD327-6DBA-4F8F-96F8-81CE7A0C863F}.Release|Any CPU.Build.0 = Release|Any CPU
		{1A94A50E-06DC-43C1-80B5-B662820EC3EB}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{1A94A50E-06DC-43C1-80B5-B662820EC3EB}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{1A94A50E-06DC-43C1-80B5-B662820EC3EB}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{1A94A50E-06DC-43C1-80B5-B662820EC3EB}.Release|Any CPU.Build.0 = Release|Any CPU
		{C956DD76-69C8-4A9C-83EA-D17DF83340FD}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{C956DD76-69C8-4A9C-83EA-D17DF83340FD}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{C956DD76-69C8-4A9C-83EA-D17DF83340FD}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{C956DD76-69C8-4A9C-83EA-D17DF83340FD}.Release|Any CPU.Build.0 = Release|Any CPU
		{068855E8-9240-4F1A-910B-CF825794513B}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{068855E8-9240-4F1A-910B-CF825794513B}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{068855E8-9240-4F1A-910B-CF825794513B}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{068855E8-9240-4F1A-910B-CF825794513B}.Release|Any CPU.Build.0 = Release|Any CPU
		{50B2631D-129C-47B3-A587-029CCD6099BC}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{50B2631D-129C-47B3-A587-029CCD6099BC}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{50B2631D-129C-47B3-A587-029CCD6099BC}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{50B2631D-129C-47B3-A587-029CCD6099BC}.Release|Any CPU.Build.0 = Release|Any CPU
		{5F1B28C6-8D0C-4155-92D0-252F7EA5F674}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{5F1B28C6-8D0C-4155-92D0-252F7EA5F674}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{5F1B28C6-8D0C-4155-92D0-252F7EA5F674}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{5F1B28C6-8D0C-4155-92D0-252F7EA5F674}.Release|Any CPU.Build.0 = Release|Any CPU
		{42F719ED-8413-4895-B5B4-5AB56079BC66}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{42F719ED-8413-4895-B5B4-5AB56079BC66}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{42F719ED-8413-4895-B5B4-5AB56079BC66}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{42F719ED-8413-4895-B5B4-5AB56079BC66}.Release|Any CPU.Build.0 = Release|Any CPU
		{520659C8-C734-4298-A3DA-B539DB9DFC0B}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{520659C8-C734-4298-A3DA-B539DB9DFC0B}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{520659C8-C734-4298-A3DA-B539DB9DFC0B}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{520659C8-C734-4298-A3DA-B539DB9DFC0B}.Release|Any CPU.Build.0 = Release|Any CPU
		{4164BDF7-F527-4E85-9CE6-E3C2D7426A27}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{4164BDF7-F527-4E85-9CE6-E3C2D7426A27}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{4164BDF7-F527-4E85-9CE6-E3C2D7426A27}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{4164BDF7-F527-4E85-9CE6-E3C2D7426A27}.Release|Any CPU.Build.0 = Release|Any CPU
		{3B5A0094-670D-4BB1-BFDD-61B88A8773DC}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{3B5A0094-670D-4BB1-BFDD-61B88A8773DC}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{3B5A0094-670D-4BB1-BFDD-61B88A8773DC}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{3B5A0094-670D-4BB1-BFDD-61B88A8773DC}.Release|Any CPU.Build.0 = Release|Any CPU
		{1FE30EB9-74A9-47F5-A9F6-7B1FAB672D81}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{1FE30EB9-74A9-47F5-A9F6-7B1FAB672D81}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{1FE30EB9-74A9-47F5-A9F6-7B1FAB672D81}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{1FE30EB9-74A9-47F5-A9F6-7B1FAB672D81}.Release|Any CPU.Build.0 = Release|Any CPU
		{91853F21-9CD9-4132-BC29-A7D5D84FFFE7}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{91853F21-9CD9-4132-BC29-A7D5D84FFFE7}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{91853F21-9CD9-4132-BC29-A7D5D84FFFE7}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{91853F21-9CD9-4132-BC29-A7D5D84FFFE7}.Release|Any CPU.Build.0 = Release|Any CPU
		{E512F4D9-9375-480F-A2F6-A46509F9D824}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{E512F4D9-9375-480F-A2F6-A46509F9D824}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{E512F4D9-9375-480F-A2F6-A46509F9D824}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{E512F4D9-9375-480F-A2F6-A46509F9D824}.Release|Any CPU.Build.0 = Release|Any CPU
		{EF480016-9127-4916-8735-D2466BDBC582}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{EF480016-9127-4916-8735-D2466BDBC582}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{EF480016-9127-4916-8735-D2466BDBC582}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{EF480016-9127-4916-8735-D2466BDBC582}.Release|Any CPU.Build.0 = Release|Any CPU
		{AA94D832-1CCC-4715-95A9-A483F23A1A5D}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{AA94D832-1CCC-4715-95A9-A483F23A1A5D}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{AA94D832-1CCC-4715-95A9-A483F23A1A5D}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{AA94D832-1CCC-4715-95A9-A483F23A1A5D}.Release|Any CPU.Build.0 = Release|Any CPU
		{9E701B08-5AE8-4C25-B1B4-0E205A5235EE}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{9E701B08-5AE8-4C25-B1B4-0E205A5235EE}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{9E701B08-5AE8-4C25-B1B4-0E205A5235EE}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{9E701B08-5AE8-4C25-B1B4-0E205A5235EE}.Release|Any CPU.Build.0 = Release|Any CPU
		{1D2A6256-9BAC-475C-91A5-45C0AE3CEFB5}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{1D2A6256-9BAC-475C-91A5-45C0AE3CEFB5}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{1D2A6256-9BAC-475C-91A5-45C0AE3CEFB5}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{1D2A6256-9BAC-475C-91A5-45C0AE3CEFB5}.Release|Any CPU.Build.0 = Release|Any CPU
		{D24A8C98-1F46-4758-9651-F5990D3D4673}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{D24A8C98-1F46-4758-9651-F5990D3D4673}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{D24A8C98-1F46-4758-9651-F5990D3D4673}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{D24A8C98-1F46-4758-9651-F5990D3D4673}.Release|Any CPU.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(NestedProjects) = preSolution
		{50B2631D-129C-47B3-A587-029CCD6099BC} = {04DBDB01-70F4-4E06-B468-8F87850B22BE}
		{5F1B28C6-8D0C-4155-92D0-252F7EA5F674} = {04DBDB01-70F4-4E06-B468-8F87850B22BE}
		{1FE30EB9-74A9-47F5-A9F6-7B1FAB672D81} = {04DBDB01-70F4-4E06-B468-8F87850B22BE}
		{91853F21-9CD9-4132-BC29-A7D5D84FFFE7} = {04DBDB01-70F4-4E06-B468-8F87850B22BE}
		{E512F4D9-9375-480F-A2F6-A46509F9D824} = {04DBDB01-70F4-4E06-B468-8F87850B22BE}
		{EF480016-9127-4916-8735-D2466BDBC582} = {04DBDB01-70F4-4E06-B468-8F87850B22BE}
		{C7F8F66B-36F6-4087-9425-AED1F8D964ED} = {CA9AC87F-097E-4F15-8393-4BC07735A5B0}
		{554AD327-6DBA-4F8F-96F8-81CE7A0C863F} = {C7F8F66B-36F6-4087-9425-AED1F8D964ED}
		{42F719ED-8413-4895-B5B4-5AB56079BC66} = {C7F8F66B-36F6-4087-9425-AED1F8D964ED}
		{C956DD76-69C8-4A9C-83EA-D17DF83340FD} = {C7F8F66B-36F6-4087-9425-AED1F8D964ED}
		{AA94D832-1CCC-4715-95A9-A483F23A1A5D} = {C7F8F66B-36F6-4087-9425-AED1F8D964ED}
		{1A94A50E-06DC-43C1-80B5-B662820EC3EB} = {AD9644A0-B8AA-40EC-A8A2-D28BE8B80B9E}
		{520659C8-C734-4298-A3DA-B539DB9DFC0B} = {AD9644A0-B8AA-40EC-A8A2-D28BE8B80B9E}
		{4164BDF7-F527-4E85-9CE6-E3C2D7426A27} = {AD9644A0-B8AA-40EC-A8A2-D28BE8B80B9E}
		{3B5A0094-670D-4BB1-BFDD-61B88A8773DC} = {AD9644A0-B8AA-40EC-A8A2-D28BE8B80B9E}
		{068855E8-9240-4F1A-910B-CF825794513B} = {AD9644A0-B8AA-40EC-A8A2-D28BE8B80B9E}
		{AD9644A0-B8AA-40EC-A8A2-D28BE8B80B9E} = {CA9AC87F-097E-4F15-8393-4BC07735A5B0}
		{9E701B08-5AE8-4C25-B1B4-0E205A5235EE} = {AD9644A0-B8AA-40EC-A8A2-D28BE8B80B9E}
		{1D2A6256-9BAC-475C-91A5-45C0AE3CEFB5} = {AD9644A0-B8AA-40EC-A8A2-D28BE8B80B9E}
		{D24A8C98-1F46-4758-9651-F5990D3D4673} = {AD9644A0-B8AA-40EC-A8A2-D28BE8B80B9E}
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {28315BFD-90E7-4E14-A2EA-F3D23AF4126F}
	EndGlobalSection
EndGlobal
